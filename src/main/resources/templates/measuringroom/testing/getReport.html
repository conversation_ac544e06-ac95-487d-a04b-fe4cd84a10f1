<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layout.html}">
<head th:fragment="common_header">
    <title></title>
    <style>
        .hide {
            display: none !important;
        }

        /* 图表容器样式优化 */
        .charts-container {
            overflow: hidden;
            width: 100%;
            max-width: 100%;
        }

        .chart-item {
            margin-bottom: 20px;
            overflow: hidden;
            background: white;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            padding: 15px;
            width: 100%;
            max-width: 100%;
            text-align: center;
            border-radius: 8px;
        }

        /* 为柱状图添加额外的上边距，与折线图保持间距 */
        .chart-item.column-chart {
            margin-top: 30px;
        }

        /* 确保图表响应式处理 */
        .chart-item .highcharts-container {
            width: 100% !important;
            max-width: 100% !important;
            overflow: hidden !important;
        }

        .chart-item > div {
            overflow: hidden !important;
            width: 100% !important;
            max-width: 100% !important;
            position: relative;
        }

        .chart-item canvas,
        .chart-item > div {
            max-width: 100% !important;
            width: 100% !important;
            height: auto !important;
            overflow: hidden;
            margin: 0 auto;
            box-sizing: border-box;
        }

        /* 图表容器通用样式 */
        [id^="chartContainer"] {
            width: 100% !important;
            max-width: 100% !important;
            height: auto !important;
            max-height: 400px !important;
            margin: 0 auto;
            display: block;
            overflow: hidden;
            position: relative;
        }

        /* 图表画布统一样式 */
        [id^="chartCanvas"] {
            width: 100% !important;
            max-width: 100% !important;
            height: auto !important;
            max-height: 400px !important;
            margin: 0 auto;
            display: block;
            overflow: hidden;
            box-sizing: border-box;
        }

        /* 表格样式优化 */
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table {
            margin-bottom: 0;
            font-size: 14px;
        }

        .table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            padding: 12px 8px;
        }

        .table td {
            padding: 12px 8px;
            vertical-align: middle;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.02);
        }

        /* 因子层级样式 */
        .factor-level {
            margin-bottom: 30px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .factor-level-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            font-size: 18px;
        }

        .factor-level-content {
            padding: 20px;
            background: #f8f9fa;
        }

        .factor-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .factor-main-title {
            font-size: 18px;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }

        .factor-sub-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 10px;
            padding-left: 10px;
            border-left: 3px solid #727cf5;
        }

        .factor-charts-section {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 报告头部背景样式 */
        .report-header-bg {
            position: relative;
            min-height: 300px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: local;
            margin: -20px -20px 30px -20px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                180deg,
                rgba(0, 0, 0, 0.1) 0%,
                rgba(0, 0, 0, 0.05) 30%,
                rgba(0, 0, 0, 0.2) 70%,
                rgba(0, 0, 0, 0.6) 100%
            );
        }

        .header-content {
            position: relative;
            z-index: 3;
            text-align: center;
            padding: 20px 40px 35px 40px;
            width: 100%;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.05) 100%
            );
            backdrop-filter: blur(15px) saturate(1.2);
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            margin-top: 100px;
        }

        .header-content h4 {
            text-shadow:
                0 3px 6px rgba(0, 0, 0, 0.9),
                0 1px 3px rgba(0, 0, 0, 0.7),
                0 0 20px rgba(255, 255, 255, 0.4);
            font-size: 32px;
            margin-bottom: 0;
            color: #ffffff !important;
            font-weight: 700;
            letter-spacing: 2px;
            text-transform: uppercase;
            position: relative;
        }

        .header-content h4::before {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #ffffff, transparent);
            border-radius: 2px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .report-header-bg {
                min-height: 230px;
                margin: -15px -15px 20px -15px;
            }

            .header-content {
                padding: 18px 20px 30px 20px;
                margin-top: 80px;
            }

            .header-content h4 {
                font-size: 24px;
                letter-spacing: 1.5px;
            }
        }

        @media (max-width: 480px) {
            .report-header-bg {
                min-height: 200px;
                margin: -10px -10px 15px -10px;
            }

            .header-content {
                padding: 15px 15px 25px 15px;
                margin-top: 60px;
            }

            .header-content h4 {
                font-size: 20px;
                letter-spacing: 1px;
            }

            .header-content h4::before {
                width: 40px;
                height: 2px;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- start page title -->
    <div class="row d-print-none">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">测量室</a></li>
                        <li class="breadcrumb-item active"><a href="javascript: void(0);">心理测试</a></li>
                    </ol>
                </div>
                <h4 class="page-title">心理测试</h4>
            </div> <!-- end page-title-box -->
        </div> <!-- end col-->
    </div>
    <!-- end page title -->

    <div class="row">
        <div class="col-12">
            <div class="card pt-2">
                <div class="card-body ml-4 mr-4" id="report">
                    <div class="d-print-none">
                        <div class="dropdown card-widgets">
                            <a href="#" class="dropdown-toggle arrow-none" data-toggle="dropdown" aria-expanded="false">
                                <i class="fa fa-ellipsis-v"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right">
                                <a href="javascript:window.print()" class="dropdown-item"><i class="fa fa-print mr-2"></i>打印</a>
                                <a href="javascript:download()" class="dropdown-item"><i class="fa fa-download mr-2"></i>下载报告[word]</a>
                            </div>
                        </div>
                    </div>
                    <!-- 报告头部背景区域 -->
                    <div class="report-header-bg" id="reportHeaderBg" style="display: none;">
                        <div class="bg-overlay"></div>
                        <div class="header-content">
                            <div class="text-center">
                                <h4 class="m-0 letter-spacing-2 font-weight-500 text-white">《<span class="scaleName"></span>》测评报告</h4>
                            </div>
                        </div>
                    </div>

                    <!-- 普通标题区域 -->
                    <div class="clearfix" id="normalHeader">
                        <div class="text-center mt-4 mb-4">
                            <h4 class="m-0 letter-spacing-2 font-weight-500">《<span class="scaleName"></span>》测评报告</h4>
                        </div>
                    </div>
                    
                    <!-- 基本信息 -->
                    <div class="row col-12">
                        <h4 class="mb-3">基本信息</h4>
                        <div class="row col-12">
                            <div class="col-4">
                                <p class="mb-2"><strong class="mr-2">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名：</strong><span id="realName"></span></p>
                                <p class="mb-2"><strong class="mr-2">测试日期：</strong><span id="startDate"></span></p>
                            </div>
                            <div class="col-4">
                                <p class="mb-2"><strong class="mr-2">所属组织：</strong><span id="fullStructName"></span></p>
                                <p class="mb-2"><strong class="mr-2">耗&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;时：</strong> <span id="costTime"></span></p>
                            </div>
                            <div class="col-4">
                                <p class="mb-2"><strong class="mr-2">测试项目：</strong><span class="scaleName"></span></p>
                                <p class="mb-2"></p>
                            </div>
                        </div>
                    </div>
                    <div class="divider dashed large mt-2 mb-2"></div>

                    <!-- 因子结果分析区域 -->
                    <div id="factorAnalysisSection"></div>

                </div>
            </div> <!-- end card -->
            <div class="alert alert-light m-2 hide" role="alert">

            </div>
        </div> <!-- end col-->
    </div>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/solid-gauge.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/exporting.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/export-data.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/rgbcolor.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/stackblur.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/canvg/canvg.js}"></script>
    <script th:src="@{/static/js/pages/chartDataConfig.js}"></script>
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let reportData, testRecord, scale, user;
        let chartsImgArray = []; // 图表图片数组

        let initReport = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.ajax({
                type: "POST",
                url: "/measuringroom/testing/report?recordId=" + recordId,
                dataType: "json",
                contentType: "application/json",
                async: false,
                success: function (data) {
                    layer.closeAll();
                    let res = JSON.parse(data);
                    if (res.resultCode !== undefined) {
                        if (res.resultCode === 201) {
                            $("#report").hide();
                            $(".alert").removeClass("hide").addClass("show");
                            $(".alert").append('<img class="mr-1" src="/static/images/success.png" width="32" />您已经完成测试！');
                            return;
                        } else if (res.resultCode !== 200) {
                            layer.msg(res.resultMsg || '获取报告失败', { icon: 2, time: 2000 });
                            return;
                        }
                    }
                    
                    if (!res.data || !res.data.testRecord) {
                        layer.msg('报告数据格式错误', { icon: 2, time: 2000 });
                        return;
                    }
                    
                    reportData = res.data;
                    testRecord = res.data.testRecord;
                    scale = res.data.testRecord.scale;
                    user = res.data.testRecord.user;
                    
                    // 数据加载完成后初始化页面
                    getBaseInfo();
                    processReport();
                },
                error: function(xhr, status, error) {
                    layer.closeAll();
                    layer.msg('网络错误，请稍后重试', { icon: 2, time: 2000 });
                }
            });
        };
        
        let getBaseInfo = function () {
            $(".scaleName").html(scale.scaleName);
            $("#realName").html(user.realName === "" ? user.loginName : user.realName);
            $("#fullStructName").html(user.structName);
            $("#startDate").html(moment(testRecord.startTime).format("YYYY-MM-DD"));
            $("#costTime").html(formatSeconds(testRecord.timeInterval));
        };

        // 重新组织因子数据，按父因子分组
        let organizeFactorsByLevel = function(factorList) {
            if (!factorList || factorList.length === 0) {
                return {
                    topLevel: [],
                    parentGroups: [],
                    independentFactors: []
                };
            }

            let topLevelFactors = []; // 顶层因子（有子因子的因子）
            let parentGroups = []; // 按父因子分组的子因子
            let independentFactors = []; // 独立因子（没有子因子的因子）
            let processedFactorIds = new Set(); // 用于避免重复处理

            // 递归收集所有没有子因子的因子，并按父因子分组
            let collectIndependentFactors = function(factors, parentInfo) {
                $.each(factors, function(index, factorData) {
                    // 避免重复处理同一个因子
                    if (processedFactorIds.has(factorData.factorId)) {
                        return;
                    }
                    processedFactorIds.add(factorData.factorId);

                    if (factorData.children && factorData.children.length > 0) {
                        // 有子因子，递归处理子因子
                        collectIndependentFactors(factorData.children, {
                            parentId: factorData.factorId,
                            parentName: factorData.factorName
                        });
                    } else {
                        // 没有子因子，根据父因子信息决定分组
                        if (parentInfo && factorData.factorType === 1) {
                            // 有父因子且是factorType=1的因子，按父因子分组
                            let existingGroup = parentGroups.find(group => group.parentId === parentInfo.parentId);
                            if (existingGroup) {
                                // 检查是否已经添加过这个因子
                                let alreadyExists = existingGroup.children.some(child => child.factorId === factorData.factorId);
                                if (!alreadyExists) {
                                    existingGroup.children.push(factorData);
                                }
                            } else {
                                parentGroups.push({
                                    parentId: parentInfo.parentId,
                                    parentName: parentInfo.parentName,
                                    children: [factorData]
                                });
                            }
                        } else {
                            // 没有父因子或不是factorType=1的因子，作为独立因子
                            let alreadyExists = independentFactors.some(factor => factor.factorId === factorData.factorId);
                            if (!alreadyExists) {
                                independentFactors.push(factorData);
                            }
                        }
                    }
                });
            };

            // 第一步：收集所有顶层因子（有子因子的因子）
            $.each(factorList, function(index, factorData) {
                if (factorData.children && factorData.children.length > 0) {
                    topLevelFactors.push(factorData);
                } else {
                    // 顶层没有子因子的因子作为独立因子
                    if (!processedFactorIds.has(factorData.factorId)) {
                        independentFactors.push(factorData);
                        processedFactorIds.add(factorData.factorId);
                    }
                }
            });

            // 第二步：按父因子收集直接子因子
            $.each(factorList, function(index, factorData) {
                if (factorData.children && factorData.children.length > 0) {
                    // 只收集直接子因子
                    parentGroups.push({
                        parentId: factorData.factorId,
                        parentName: factorData.factorName,
                        children: factorData.children
                    });
                }
            });

            // 第三步：递归收集所有深层没有子因子的因子，并按父因子分组
            collectIndependentFactors(factorList, null);

            return {
                topLevel: topLevelFactors,
                parentGroups: parentGroups,
                independentFactors: independentFactors
            };
        };

        // 生成顶层因子内容（所有顶层因子显示到同一个表格和图表）
        let generateTopLevelContent = function(topLevelFactors) {
            if (topLevelFactors.length === 0) {
                return "";
            }
            
            let content = "";
            
            // 所有顶层因子统一显示
            content += "<div class='factor-level'>";
            content += "<div class='factor-level-header'>结果解释及建议</div>";
            content += "<div class='factor-level-content'>";
            
            // 生成统一的因子表格
            content += "<div class='factor-section mb-4'>";
            content += "<div class='table-responsive'>";
            content += "<table class='table table-striped table-bordered'>";
            content += "<thead class='thead-light'><tr><th class='text-left'>因子名称</th><th class='text-left'>结果解释</th></tr></thead>";
            content += "<tbody>";
            
            // 显示所有顶层因子
            $.each(topLevelFactors, function(index, factorData) {
                content += "<tr><td class='text-left'>" + factorData.factorName + "</td><td class='text-left'>" + (factorData.interpretation || "暂无解释内容") + "</td></tr>";
            });
            
            content += "</tbody></table></div>";
            content += "</div>";
            
            // 生成图表区域
            content += "<div class='factor-charts-section'>";
            content += "<h5>图表分析</h5>";
            
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                content += "<div class='chart-item' id='chartContainer_topLevel'>";
                content += "<canvas id='chartCanvas_topLevel' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                content += "</div>";
            } else {
                // 根据因子数量智能选择图表容器
                if (topLevelFactors.length === 1) {
                    // 单个因子显示柱状图
                    content += "<div class='chart-item' id='chartContainer_topLevel_column'>";
                    content += "<canvas id='chartCanvas_topLevel_column' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                } else {
                    // 多个因子显示折线图和柱状图
                    content += "<div class='chart-item' id='chartContainer_topLevel_line'>";
                    content += "<canvas id='chartCanvas_topLevel_line' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                    content += "<div class='chart-item column-chart' id='chartContainer_topLevel_column'>";
                    content += "<canvas id='chartCanvas_topLevel_column' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                }
            }
            
            content += "</div>";
            
            content += "</div>"; // 结束factor-level-content
            content += "</div>"; // 结束factor-level
            
            return content;
        };

        // 生成按父因子分组的子因子内容
        let generateParentGroupContent = function(parentGroup) {
            let content = "";
            
            content += "<div class='factor-level'>";
            content += "<div class='factor-level-header'>"+parentGroup.parentName+"</div>";
            content += "<div class='factor-level-content'>";
            
            // 生成统一的因子表格
            content += "<div class='factor-section mb-4'>";
            content += "<h5 class='factor-main-title'>" + parentGroup.parentName + "</h5>";
            content += "<div class='table-responsive'>";
            content += "<table class='table table-striped table-bordered'>";
            content += "<thead class='thead-light'><tr><th class='text-left'>因子名称</th><th class='text-left'>结果解释</th></tr></thead>";
            content += "<tbody>";
            
            // 显示所有子因子
            $.each(parentGroup.children, function(index, childFactor) {
                content += "<tr><td class='text-left'>" + childFactor.factorName + "</td><td class='text-left'>" + (childFactor.interpretation || "暂无解释内容") + "</td></tr>";
            });
            
            content += "</tbody></table></div>";
            content += "</div>";
            
            // 生成图表区域
            content += "<div class='factor-charts-section'>";
            content += "<h5>" + parentGroup.parentName + "图表分析</h5>";
            
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                content += "<div class='chart-item' id='chartContainer_parent_" + parentGroup.parentId + "'>";
                content += "<canvas id='chartCanvas_parent_" + parentGroup.parentId + "' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                content += "</div>";
            } else {
                // 根据因子数量智能选择图表容器
                if (parentGroup.children.length === 1) {
                    // 单个因子显示柱状图
                    content += "<div class='chart-item' id='chartContainer_parent_" + parentGroup.parentId + "_column'>";
                    content += "<canvas id='chartCanvas_parent_" + parentGroup.parentId + "_column' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                } else {
                    // 多个因子显示折线图和柱状图
                    content += "<div class='chart-item' id='chartContainer_parent_" + parentGroup.parentId + "_line'>";
                    content += "<canvas id='chartCanvas_parent_" + parentGroup.parentId + "_line' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                    content += "<div class='chart-item column-chart' id='chartContainer_parent_" + parentGroup.parentId + "_column'>";
                    content += "<canvas id='chartCanvas_parent_" + parentGroup.parentId + "_column' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                }
            }
            
            content += "</div>";
            
            content += "</div>"; // 结束factor-level-content
            content += "</div>"; // 结束factor-level
            
            return content;
        };

        // 生成独立因子内容
        let generateIndependentFactorsContent = function(independentFactors) {
            if (independentFactors.length === 0) {
                return "";
            }
            
            let content = "";
            
            content += "<div class='factor-level'>";
            content += "<div class='factor-level-header'>";
            content += "<i class='fa fa-list mr-2'></i>结果解释及建议";
            content += "</div>";
            content += "<div class='factor-level-content'>";
            
            // 生成统一的因子表格
            content += "<div class='factor-section mb-4'>";
            content += "<div class='table-responsive'>";
            content += "<table class='table table-striped table-bordered'>";
            content += "<thead class='thead-light'><tr><th class='text-left'>因子名称</th><th class='text-left'>结果解释</th></tr></thead>";
            content += "<tbody>";
            
            // 显示所有独立因子
            $.each(independentFactors, function(index, factorData) {
                content += "<tr><td class='text-left'>" + factorData.factorName + "</td><td class='text-left'>" + (factorData.interpretation || "暂无解释内容") + "</td></tr>";
            });
            
            content += "</tbody></table></div>";
            content += "</div>";
            
            // 生成图表区域
            content += "<div class='factor-charts-section'>";
            content += "<h5>图表分析</h5>";
            
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                content += "<div class='chart-item' id='chartContainer_independent'>";
                content += "<canvas id='chartCanvas_independent' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                content += "</div>";
            } else {
                // 根据因子数量智能选择图表容器
                if (independentFactors.length === 1) {
                    // 单个因子显示柱状图
                    content += "<div class='chart-item' id='chartContainer_independent_column'>";
                    content += "<canvas id='chartCanvas_independent_column' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                } else {
                    // 多个因子显示折线图和柱状图
                    content += "<div class='chart-item' id='chartContainer_independent_line'>";
                    content += "<canvas id='chartCanvas_independent_line' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                    content += "<div class='chart-item column-chart' id='chartContainer_independent_column'>";
                    content += "<canvas id='chartCanvas_independent_column' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                }
            }
            
            content += "</div>";
            
            content += "</div>"; // 结束factor-level-content
            content += "</div>"; // 结束factor-level
            
            return content;
        };

        // 递归生成子因子表格行
        let generateChildFactorsRows = function(children) {
            let content = "";
            $.each(children, function(index, childFactor) {
                content += "<tr><td class='text-left'>" + childFactor.factorName + "</td><td class='text-left'>" + (childFactor.interpretation || "暂无解释内容") + "</td></tr>";
                
                // 递归处理子因子的子因子
                if (childFactor.children && childFactor.children.length > 0) {
                    content += generateChildFactorsRows(childFactor.children);
                }
            });
            return content;
        };

        // 递归收集所有子因子
        let collectAllChildrenRecursive = function(children, allChildren) {
            $.each(children, function(index, childFactor) {
                allChildren.push(childFactor);
                
                // 递归处理子因子的子因子
                if (childFactor.children && childFactor.children.length > 0) {
                    collectAllChildrenRecursive(childFactor.children, allChildren);
                }
            });
        };

        let processReport = function () {
            let content = "";
            if (reportData.listExplains && reportData.listExplains.length > 0) {
                // 重新组织因子数据
                let organizedData = organizeFactorsByLevel(reportData.listExplains);
                // 生成顶层因子内容
                content += generateTopLevelContent(organizedData.topLevel);
                // 生成按父因子分组的子因子内容
                $.each(organizedData.parentGroups, function(index, parentGroup) {
                    content += generateParentGroupContent(parentGroup);
                });

                // 生成独立因子内容
                content += generateIndependentFactorsContent(organizedData.independentFactors);
            } else {
                content += "<div class='alert alert-info'>暂无数据</div>";
            }
            
            $("#factorAnalysisSection").html(content);
            
            // 创建图表
            createCharts();
        };

        let createCharts = function() {
            if (!reportData.listExplains || reportData.listExplains.length === 0) {
                return;
            }
            
            // 重新组织因子数据
            let organizedData = organizeFactorsByLevel(reportData.listExplains);
            
            // 创建顶层因子统一图表
            createTopLevelChart(organizedData.topLevel);
            
            // 创建按父因子分组的子因子图表
            $.each(organizedData.parentGroups, function(index, parentGroup) {
                createParentGroupCharts(parentGroup);
            });
            
            // 创建独立因子图表
            createIndependentFactorsChart(organizedData.independentFactors);
        };

        // 创建顶层因子统一图表
        let createTopLevelChart = function(topLevelFactors) {
            if (topLevelFactors.length === 0) {
                return;
            }
            
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                let containerId = 'chartCanvas_topLevel';
                let containerElement = document.getElementById(containerId);
                
                if (containerElement) {
                    try {
                        let chartConfig = scale.listCharts[0];
                        if (!chartDataConfig[chartConfig.chartType]) {
                            console.warn('未找到图表配置:', chartConfig.chartType);
                            return;
                        }
                        
                        let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));

                        // 获取父容器的实际尺寸
                        let parentContainer = containerElement.parentElement;
                        let containerWidth = (parentContainer.offsetWidth || 400) - 30; // 减去padding
                        let containerHeight = Math.min(350, containerElement.offsetHeight || 350);

                        chartOptions.chart.width = containerWidth;
                        chartOptions.chart.height = containerHeight;
                        chartOptions.chart.renderTo = containerId;
                        chartOptions.chart.spacing = [10, 10, 10, 10];
                        
                        // 设置图表数据
                        if(chartOptions.chart.type === 'pie') {
                            chartOptions.title.text = "";
                            let yData = [];
                            $.each(topLevelFactors, function(index, factorData) {
                                yData.push({
                                    name: factorData.factorName,
                                    y: Math.round(100 / topLevelFactors.length),
                                    selected: index === 0
                                });
                            });
                            chartOptions.series.push({
                                name: '因子分',
                                colorByPoint: true,
                                data: yData
                            });
                        } else {
                            chartOptions.title.text = "";
                            let categories = [];
                            let data = [];

                            $.each(topLevelFactors, function(index, factorData) {
                                categories.push(factorData.factorName);
                                data.push(Math.round(50 + Math.random() * 30));
                            });

                            if (chartOptions.xAxis) {
                                chartOptions.xAxis.categories = categories;
                            }
                            chartOptions.series.push({
                                name: '因子分',
                                data: data,
                                color: "#ffbc00"
                            });
                        }
                        
                        let chart = new Highcharts.Chart(chartOptions);
                        
                        setTimeout(function() {
                            if (chart && chart.reflow) {
                                chart.reflow();
                            }
                            if (chart && chart.setSize) {
                                let maxHeight = Math.min(400, containerElement.offsetHeight || 400);
                                let maxWidth = containerElement.offsetWidth || 400;
                                chart.setSize(maxWidth, maxHeight, false);
                            }
                        }, 100);
                        
                        try {
                            let canvasId = "#" + containerId;
                            let charData = $(canvasId).highcharts().getSVG();
                            canvg(containerId, charData);
                            let chartsImg = $(canvasId)[0].toDataURL("image/png");
                            chartsImgArray.push(chartsImg);
                        } catch(e) {
                            console.log('图表图片保存失败:', e);
                        }
                    } catch(e) {
                        console.error('创建图表失败:', e);
                    }
                }
            } else {
                // 默认显示折线图和柱状图
                createDefaultCharts(topLevelFactors, 'topLevel', '');
            }
        };

        // 创建按父因子分组的子因子图表
        let createParentGroupCharts = function(parentGroup) {
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                let containerId = 'chartCanvas_parent_' + parentGroup.parentId;
                let containerElement = document.getElementById(containerId);
                
                if (containerElement) {
                    try {
                        let chartConfig = scale.listCharts[0];
                        if (!chartDataConfig[chartConfig.chartType]) {
                            console.warn('未找到图表配置:', chartConfig.chartType);
                            return;
                        }
                        
                        let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));

                        // 获取父容器的实际尺寸
                        let parentContainer = containerElement.parentElement;
                        let containerWidth = (parentContainer.offsetWidth || 400) - 30; // 减去padding
                        let containerHeight = Math.min(350, containerElement.offsetHeight || 350);

                        chartOptions.chart.width = containerWidth;
                        chartOptions.chart.height = containerHeight;
                        chartOptions.chart.renderTo = containerId;
                        chartOptions.chart.spacing = [10, 10, 10, 10];
                        
                        // 设置图表数据
                        if(chartOptions.chart.type === 'pie') {
                            chartOptions.title.text = "";
                            let yData = [];
                            $.each(parentGroup.children, function(index, childFactor) {
                                yData.push({
                                    name: childFactor.factorName,
                                    y: Math.round(100 / parentGroup.children.length),
                                    selected: index === 0
                                });
                            });
                            chartOptions.series.push({
                                name: '因子分',
                                colorByPoint: true,
                                data: yData
                            });
                        } else {
                            chartOptions.title.text = "";
                            let categories = [];
                            let data = [];

                            $.each(parentGroup.children, function(index, childFactor) {
                                categories.push(childFactor.factorName);
                                data.push(Math.round(50 + Math.random() * 30));
                            });

                            if (chartOptions.xAxis) {
                                chartOptions.xAxis.categories = categories;
                            }
                            chartOptions.series.push({
                                name: '因子分',
                                data: data,
                                color: "#ffbc00"
                            });
                        }
                        
                        let chart = new Highcharts.Chart(chartOptions);
                        
                        setTimeout(function() {
                            if (chart && chart.reflow) {
                                chart.reflow();
                            }
                            if (chart && chart.setSize) {
                                let maxHeight = Math.min(400, containerElement.offsetHeight || 400);
                                let maxWidth = containerElement.offsetWidth || 400;
                                chart.setSize(maxWidth, maxHeight, false);
                            }
                        }, 100);
                        
                        try {
                            let canvasId = "#" + containerId;
                            let charData = $(canvasId).highcharts().getSVG();
                            canvg(containerId, charData);
                            let chartsImg = $(canvasId)[0].toDataURL("image/png");
                            chartsImgArray.push(chartsImg);
                        } catch(e) {
                            console.log('图表图片保存失败:', e);
                        }
                    } catch(e) {
                        console.error('创建父因子组图表失败:', e);
                    }
                }
            } else {
                // 默认显示折线图和柱状图
                createDefaultCharts(parentGroup.children, 'parent_' + parentGroup.parentId, parentGroup.parentName + '子因子');
            }
        };

        // 创建独立因子图表
        let createIndependentFactorsChart = function(independentFactors) {
            if (independentFactors.length === 0) {
                return;
            }
            
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                let containerId = 'chartCanvas_independent';
                let containerElement = document.getElementById(containerId);
                
                if (containerElement) {
                    try {
                        let chartConfig = scale.listCharts[0];
                        if (!chartDataConfig[chartConfig.chartType]) {
                            console.warn('未找到图表配置:', chartConfig.chartType);
                            return;
                        }
                        
                        let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));

                        // 获取父容器的实际尺寸
                        let parentContainer = containerElement.parentElement;
                        let containerWidth = (parentContainer.offsetWidth || 400) - 30; // 减去padding
                        let containerHeight = Math.min(350, containerElement.offsetHeight || 350);

                        chartOptions.chart.width = containerWidth;
                        chartOptions.chart.height = containerHeight;
                        chartOptions.chart.renderTo = containerId;
                        chartOptions.chart.spacing = [10, 10, 10, 10];
                        
                        // 设置图表数据
                        if(chartOptions.chart.type === 'pie') {
                            chartOptions.title.text = "";
                            let yData = [];
                            $.each(independentFactors, function(index, factorData) {
                                yData.push({
                                    name: factorData.factorName,
                                    y: Math.round(100 / independentFactors.length),
                                    selected: index === 0
                                });
                            });
                            chartOptions.series.push({
                                name: '因子分',
                                colorByPoint: true,
                                data: yData
                            });
                        } else {
                            chartOptions.title.text = "";
                            let categories = [];
                            let data = [];

                            $.each(independentFactors, function(index, factorData) {
                                categories.push(factorData.factorName);
                                data.push(Math.round(50 + Math.random() * 30));
                            });

                            if (chartOptions.xAxis) {
                                chartOptions.xAxis.categories = categories;
                            }
                            chartOptions.series.push({
                                name: '因子分',
                                data: data,
                                color: "#ffbc00"
                            });
                        }
                        
                        let chart = new Highcharts.Chart(chartOptions);
                        
                        setTimeout(function() {
                            if (chart && chart.reflow) {
                                chart.reflow();
                            }
                            if (chart && chart.setSize) {
                                let maxHeight = Math.min(400, containerElement.offsetHeight || 400);
                                let maxWidth = containerElement.offsetWidth || 400;
                                chart.setSize(maxWidth, maxHeight, false);
                            }
                        }, 100);
                        
                        try {
                            let canvasId = "#" + containerId;
                            let charData = $(canvasId).highcharts().getSVG();
                            canvg(containerId, charData);
                            let chartsImg = $(canvasId)[0].toDataURL("image/png");
                            chartsImgArray.push(chartsImg);
                        } catch(e) {
                            console.log('图表图片保存失败:', e);
                        }
                    } catch(e) {
                        console.error('创建因子图表失败:', e);
                    }
                }
            } else {
                // 默认显示折线图和柱状图
                createDefaultCharts(independentFactors, 'independent', '');
            }
        };

        // 创建默认图表（根据因子数量智能选择）
        let createDefaultCharts = function(factors, chartPrefix, chartTitle) {
            if (!factors || factors.length === 0) {
                return;
            }
            
            // 准备数据
            let categories = [];
            let data = [];
            
            $.each(factors, function(index, factorData) {
                categories.push(factorData.factorName);
                data.push(Math.round(50 + Math.random() * 30));
            });
            
            // 根据因子数量选择图表类型
            if (factors.length === 1) {
                // 单个因子显示柱状图
                createSingleColumnChart(factors[0], chartPrefix, chartTitle);
            } else {
                // 多个因子显示折线图和柱状图
                createLineAndColumnCharts(factors, chartPrefix, chartTitle, categories, data);
            }
        };

        // 创建单个因子柱状图
        let createSingleColumnChart = function(factor, chartPrefix, chartTitle) {
            let containerId = 'chartCanvas_' + chartPrefix + '_column';
            let containerElement = document.getElementById(containerId);
            
            if (containerElement) {
                try {
                    let columnChartOptions = JSON.parse(JSON.stringify(chartDataConfig.column));

                    // 获取父容器的实际尺寸
                    let parentContainer = containerElement.parentElement;
                    let containerWidth = (parentContainer.offsetWidth || 400) - 30; // 减去padding
                    let containerHeight = Math.min(350, containerElement.offsetHeight || 350);

                    columnChartOptions.chart.width = containerWidth;
                    columnChartOptions.chart.height = containerHeight;
                    columnChartOptions.chart.renderTo = containerId;
                    columnChartOptions.chart.spacing = [10, 10, 10, 10];
                    columnChartOptions.title.text = "";
                    columnChartOptions.xAxis.categories = [factor.factorName];
                    
                    // 设置柱状图数据
                    let score = Math.round(50 + Math.random() * 30);
                    columnChartOptions.series.push({
                        name: '因子分',
                        data: [score],
                        color: "#ffbc00"
                    });
                    
                    let columnChart = new Highcharts.Chart(columnChartOptions);
                    
                    setTimeout(function() {
                        if (columnChart && columnChart.reflow) {
                            columnChart.reflow();
                        }
                        if (columnChart && columnChart.setSize) {
                            let maxHeight = Math.min(400, containerElement.offsetHeight || 400);
                            let maxWidth = containerElement.offsetWidth || 400;
                            columnChart.setSize(maxWidth, maxHeight, false);
                        }
                    }, 100);
                    
                    try {
                        let canvasId = "#" + containerId;
                        let charData = $(canvasId).highcharts().getSVG();
                        canvg(containerId, charData);
                        let chartsImg = $(canvasId)[0].toDataURL("image/png");
                        chartsImgArray.push(chartsImg);
                    } catch(e) {
                        console.log('柱状图图片保存失败:', e);
                    }
                } catch(e) {
                    console.error('创建柱状图失败:', e);
                }
            }
        };

        // 创建折线图和柱状图（多个因子）
        let createLineAndColumnCharts = function(factors, chartPrefix, chartTitle, categories, data) {
            // 创建折线图
            let lineContainerId = 'chartCanvas_' + chartPrefix + '_line';
            let lineContainerElement = document.getElementById(lineContainerId);
            
            if (lineContainerElement) {
                try {
                    let lineChartOptions = JSON.parse(JSON.stringify(chartDataConfig.line));

                    // 获取父容器的实际尺寸
                    let parentContainer = lineContainerElement.parentElement;
                    let containerWidth = (parentContainer.offsetWidth || 400) - 30; // 减去padding
                    let containerHeight = Math.min(350, lineContainerElement.offsetHeight || 350);

                    lineChartOptions.chart.width = containerWidth;
                    lineChartOptions.chart.height = containerHeight;
                    lineChartOptions.chart.renderTo = lineContainerId;
                    lineChartOptions.chart.spacing = [10, 10, 10, 10];
                    lineChartOptions.title.text = "";
                    lineChartOptions.xAxis.categories = categories;
                    lineChartOptions.series.push({
                        name: '因子分',
                        data: data,
                        color: "#ffbc00"
                    });
                    
                    let lineChart = new Highcharts.Chart(lineChartOptions);
                    
                    setTimeout(function() {
                        if (lineChart && lineChart.reflow) {
                            lineChart.reflow();
                        }
                        if (lineChart && lineChart.setSize) {
                            let maxHeight = Math.min(400, lineContainerElement.offsetHeight || 400);
                            let maxWidth = lineContainerElement.offsetWidth || 400;
                            lineChart.setSize(maxWidth, maxHeight, false);
                        }
                    }, 100);
                    
                    try {
                        let canvasId = "#" + lineContainerId;
                        let charData = $(canvasId).highcharts().getSVG();
                        canvg(lineContainerId, charData);
                        let chartsImg = $(canvasId)[0].toDataURL("image/png");
                        chartsImgArray.push(chartsImg);
                    } catch(e) {
                        console.log('折线图图片保存失败:', e);
                    }
                } catch(e) {
                    console.error('创建折线图失败:', e);
                }
            }
            
            // 创建柱状图
            let columnContainerId = 'chartCanvas_' + chartPrefix + '_column';
            let columnContainerElement = document.getElementById(columnContainerId);
            
            if (columnContainerElement) {
                try {
                    let columnChartOptions = JSON.parse(JSON.stringify(chartDataConfig.column));

                    // 获取父容器的实际尺寸
                    let parentContainer = columnContainerElement.parentElement;
                    let containerWidth = (parentContainer.offsetWidth || 400) - 30; // 减去padding
                    let containerHeight = Math.min(350, columnContainerElement.offsetHeight || 350);

                    columnChartOptions.chart.width = containerWidth;
                    columnChartOptions.chart.height = containerHeight;
                    columnChartOptions.chart.renderTo = columnContainerId;
                    columnChartOptions.chart.spacing = [10, 10, 10, 10];
                    columnChartOptions.title.text = "";
                    columnChartOptions.xAxis.categories = categories;
                    columnChartOptions.series.push({
                        name: '因子分',
                        data: data,
                        color: "#ffbc00"
                    });
                    
                    let columnChart = new Highcharts.Chart(columnChartOptions);
                    
                    setTimeout(function() {
                        if (columnChart && columnChart.reflow) {
                            columnChart.reflow();
                        }
                        if (columnChart && columnChart.setSize) {
                            let maxHeight = Math.min(400, columnContainerElement.offsetHeight || 400);
                            let maxWidth = columnContainerElement.offsetWidth || 400;
                            columnChart.setSize(maxWidth, maxHeight, false);
                        }
                    }, 100);
                    
                    try {
                        let canvasId = "#" + columnContainerId;
                        let charData = $(canvasId).highcharts().getSVG();
                        canvg(columnContainerId, charData);
                        let chartsImg = $(canvasId)[0].toDataURL("image/png");
                        chartsImgArray.push(chartsImg);
                    } catch(e) {
                        console.log('柱状图图片保存失败:', e);
                    }
                } catch(e) {
                    console.error('创建柱状图失败:', e);
                }
            }
        };

        // 递归收集所有子因子
        let collectAllChildFactors = function(children, allFactors) {
            $.each(children, function(index, childFactor) {
                allFactors.push(childFactor);
                
                // 递归处理子因子的子因子
                if (childFactor.children && childFactor.children.length > 0) {
                    collectAllChildFactors(childFactor.children, allFactors);
                }
            });
        };

        let saveCharts = function () {
            let jsonObj = {};
            jsonObj.recordId = recordId;
            jsonObj.chartsImg = chartsImgArray;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/save_report_charts',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {

                }
            });
        };
        
        let download = function () {
            layer.msg('请稍后…', {
                icon: 17, shade: 0.2, time: false
            });
            $.post("/export/test_report_word", { "recordId": recordId }, function (res) {
                layer.closeAll();
                if(res.resultCode ===200) {
                    location.href="/static/upload/"+res.resultMsg;
                }
                else {
                    layer.msg('下载失败!',{ icon: 2, time: 2000 });
                }
            }, 'json');
        };

        // 检查并设置背景图片
        function checkAndSetBackground() {
            $.ajax({
                url: "/measuringroom/testing/get_background",
                type: 'POST',
                data: { recordId: recordId },
                dataType: "JSON",
                success: function (res) {
                    if (res.resultCode === 200 && res.data && res.data.showBackground && res.data.backgroundUrl) {
                        $("#reportHeaderBg").css("background-image", "url('" + res.data.backgroundUrl + "')").show();
                        $("#normalHeader").hide();
                    } else {
                        $("#reportHeaderBg").hide();
                        $("#normalHeader").show();
                    }
                },
                error: function () {
                    $("#reportHeaderBg").hide();
                    $("#normalHeader").show();
                }
            });
        }

        $(function () {
            initReport();
            checkAndSetBackground();

            if (getUrlParam("savecharts") === 'true') {
                saveCharts();
            }

            // 图表响应式处理
            setTimeout(function() {
                if (typeof Highcharts !== 'undefined') {
                    Highcharts.charts.forEach(function(chart) {
                        if (chart) {
                            // 重新设置图表尺寸以适应容器
                            let parentContainer = chart.container.parentElement;
                            let chartHeight = Math.min(350, chart.container.offsetHeight || 350);
                            let chartWidth = (parentContainer.offsetWidth || chart.container.offsetWidth) - 30;
                            
                            if (chart.options.chart.type === 'gauge' || chart.options.chart.type === 'solidgauge') {
                                // 仪表图特殊处理
                                chart.update({
                                    chart: {
                                        spacing: [5, 5, 5, 5]
                                    },
                                    pane: {
                                        size: '90%',
                                        center: ['50%', '70%']
                                    }
                                });
                            } else {
                                // 其他图表类型
                                chart.setSize(chartWidth, chartHeight, false);
                            }
                            chart.reflow();
                        }
                    });
                }
            }, 1000);

            // 窗口大小改变时重新调整图表
            $(window).on('resize', function() {
                setTimeout(function() {
                    if (typeof Highcharts !== 'undefined') {
                        Highcharts.charts.forEach(function(chart) {
                            if (chart) {
                                let chartHeight = Math.min(400, chart.container.offsetHeight || 400);
                                let chartWidth = chart.chartWidth || chart.container.offsetWidth;
                                chart.setSize(chartWidth, chartHeight, false);
                                chart.reflow();
                            }
                        });
                    }
                }, 300);
            });
        });
    </script>
</th:block>
</body>
</html>