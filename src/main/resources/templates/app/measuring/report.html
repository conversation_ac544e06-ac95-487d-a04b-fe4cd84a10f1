<!DOCTYPE html>
<html lang="zh"
      xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layouts/layoutAppMain.html}">
<head th:fragment="common_header">
    <title></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <style>
        /* 报告头部背景样式 */
        .report-header-bg {
            position: relative;
            min-height: 280px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: local;
            margin: -16px -16px 25px -16px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .bg-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                180deg,
                rgba(0, 0, 0, 0.05) 0%,
                rgba(0, 0, 0, 0.02) 30%,
                rgba(0, 0, 0, 0.1) 70%,
                rgba(0, 0, 0, 0.3) 100%
            );
        }

        .header-content {
            position: relative;
            z-index: 3;
            width: 100%;
        }

        .report-header-bg .report-title-section {
            text-align: center;
            padding: 15px 20px 25px 20px;
            background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.15) 0%,
                rgba(255, 255, 255, 0.05) 100%
            );
            backdrop-filter: blur(15px) saturate(1.2);
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            margin: 100px 0 0 0;
            position: relative;
        }

        .report-header-bg .report-subtitle {
            text-shadow:
                0 2px 4px rgba(0, 0, 0, 0.9),
                0 1px 2px rgba(0, 0, 0, 0.7),
                0 0 15px rgba(255, 255, 255, 0.4);
            color: #ffffff !important;
            font-weight: 500;
            font-size: 16px;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .report-header-bg .report-main-title {
            text-shadow:
                0 3px 6px rgba(0, 0, 0, 0.9),
                0 1px 3px rgba(0, 0, 0, 0.7),
                0 0 20px rgba(255, 255, 255, 0.4);
            color: #ffffff !important;
            font-weight: 700;
            letter-spacing: 2px;
            font-size: 28px;
            text-transform: uppercase;
            position: relative;
            margin-bottom: 0;
        }

        .report-header-bg .report-main-title::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #ffffff, transparent);
            border-radius: 2px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .report-header-bg {
                min-height: 240px;
                margin: -16px -16px 20px -16px;
            }

            .report-header-bg .report-title-section {
                padding: 12px 16px 20px 16px;
                margin: 80px 0 0 0;
            }

            .report-header-bg .report-main-title {
                font-size: 24px;
                letter-spacing: 1.5px;
            }

            .report-header-bg .report-subtitle {
                font-size: 15px;
            }
        }

        @media (max-width: 480px) {
            .report-header-bg {
                min-height: 220px;
                margin: -16px -16px 16px -16px;
            }

            .report-header-bg .report-title-section {
                padding: 10px 12px 18px 12px;
                margin: 60px 0 0 0;
            }

            .report-header-bg .report-main-title {
                font-size: 20px;
                letter-spacing: 1px;
            }

            .report-header-bg .report-subtitle {
                font-size: 14px;
            }

            .report-header-bg .report-main-title::after {
                width: 40px;
                height: 2px;
                bottom: -5px;
            }
        }

        /* 表格优化 */
        .table-responsive {
            -webkit-overflow-scrolling: touch;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table {
            margin-bottom: 0;
            background: white;
        }

        .table thead th {
            position: sticky;
            top: 0;
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            z-index: 10;
        }

        /* 图表容器样式优化 - 统一电脑端和移动端 */
        .charts-container {
            overflow: hidden;
            width: 100%;
            max-width: 100%;
        }

        .chart-item {
            margin-bottom: 20px;
            overflow: visible;
            background: white;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            padding: 15px;
            width: 100%;
            max-width: 100%;
            text-align: center;
            border-radius: 8px;
        }

        .chart-item > div {
            overflow: visible !important;
            width: 100% !important;
            max-width: 100% !important;
        }

        .chart-item canvas,
        .chart-item > div {
            max-width: 100% !important;
            width: 100% !important;
            height: auto !important;
            overflow: visible;
            margin: 0 auto;
        }

        /* 图表容器通用样式 - 统一尺寸 */
        [id^="chartContainer"] {
            width: 100% !important;
            max-width: 100% !important;
            height: auto !important;
            max-height: 300px !important;
            margin: 0 auto;
            display: block;
        }

        /* 图表画布统一样式 */
        [id^="chartCanvas"] {
            width: 100% !important;
            max-width: 100% !important;
            height: auto !important;
            max-height: 300px !important;
            margin: 0 auto;
            display: block;
        }

        /* 因子层级样式 - 统一电脑端和移动端 */
        .factor-level {
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .factor-level-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            font-size: 16px;
            display: flex;
            align-items: center;
        }

        .factor-level-header i {
            margin-right: 8px;
        }

        .factor-level-content {
            padding: 20px;
            background: #f8f9fa;
        }

        .factor-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .factor-main-title {
            font-size: 16px;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #eee;
        }

        .factor-sub-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 12px;
            padding-left: 12px;
            border-left: 3px solid #727cf5;
        }

        .factor-charts-section {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .factor-charts-section h5 {
            font-size: 15px;
            margin-bottom: 12px;
            color: #495057;
            font-weight: 600;
        }

        /* 滚动提示 */
        .scroll-hint {
            text-align: center;
            font-size: 12px;
            color: #666;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
        }

        /* 表格滚动指示器 */
        .table-responsive.scrollable {
            position: relative;
        }

        .table-responsive.scrollable::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 20px;
            background: linear-gradient(to left, rgba(255,255,255,0.8), transparent);
            pointer-events: none;
        }

        /* 移动端专用适配样式 */
        @media (max-width: 768px) {
            /* 移动端整体布局优化 */
            .appContent {
                padding: 10px;
            }

            /* 移动端因子层级样式 */
            .factor-level {
                margin-bottom: 15px;
                border-radius: 6px;
            }

            .factor-level-header {
                padding: 12px 16px;
                font-size: 15px;
                border-radius: 6px 6px 0 0;
            }

            .factor-level-content {
                padding: 16px;
                border-radius: 0 0 6px 6px;
            }

            .factor-main-title {
                font-size: 15px;
                margin-bottom: 12px;
                padding-bottom: 6px;
            }

            .factor-charts-section {
                padding: 12px;
                margin-top: 16px;
                border-radius: 6px;
            }

            .factor-charts-section h5 {
                font-size: 14px;
                margin-bottom: 10px;
            }

            /* 移动端图表优化 */
            .chart-item {
                padding: 12px;
                margin-bottom: 16px;
                border-radius: 6px;
            }

            [id^="chartContainer"],
            [id^="chartCanvas"] {
                max-height: 250px !important;
                border-radius: 4px;
            }

            /* 移动端表格优化 */
            .table-responsive {
                border-radius: 6px;
                margin-bottom: 15px;
            }

            .table {
                font-size: 13px;
            }

            .table th {
                padding: 10px 8px;
                font-size: 13px;
            }

            .table td {
                padding: 10px 8px;
                font-size: 13px;
            }

            /* 移动端滚动提示优化 */
            .scroll-hint {
                font-size: 11px;
                padding: 6px 8px;
                margin-bottom: 6px;
                border-radius: 4px;
            }

            /* 移动端按钮优化 */
            .btn {
                padding: 8px 16px;
                font-size: 14px;
                border-radius: 4px;
            }

            /* 移动端卡片优化 */
            .card {
                border-radius: 6px;
                margin-bottom: 15px;
            }
        }

        /* 小屏手机专用适配样式 */
        @media (max-width: 480px) {
            /* 小屏手机整体布局 */
            .appContent {
                padding: 8px;
            }

            /* 小屏手机因子层级样式 */
            .factor-level {
                margin-bottom: 12px;
                border-radius: 4px;
            }

            .factor-level-header {
                padding: 10px 12px;
                font-size: 14px;
                border-radius: 4px 4px 0 0;
            }

            .factor-level-content {
                padding: 12px;
                border-radius: 0 0 4px 4px;
            }

            .factor-main-title {
                font-size: 14px;
                margin-bottom: 10px;
                padding-bottom: 5px;
            }

            .factor-charts-section {
                padding: 10px;
                margin-top: 12px;
                border-radius: 4px;
            }

            .factor-charts-section h5 {
                font-size: 13px;
                margin-bottom: 8px;
            }

            /* 小屏手机图表优化 */
            .chart-item {
                padding: 8px;
                margin-bottom: 12px;
                border-radius: 4px;
            }

            [id^="chartContainer"],
            [id^="chartCanvas"] {
                max-height: 220px !important;
                border-radius: 3px;
            }

            /* 小屏手机表格优化 */
            .table-responsive {
                border-radius: 4px;
                margin-bottom: 12px;
            }

            .table {
                font-size: 12px;
            }

            .table th {
                padding: 8px 6px;
                font-size: 12px;
            }

            .table td {
                padding: 8px 6px;
                font-size: 12px;
            }

            /* 小屏手机滚动提示优化 */
            .scroll-hint {
                font-size: 10px;
                padding: 5px 6px;
                margin-bottom: 5px;
                border-radius: 3px;
            }

            /* 小屏手机按钮优化 */
            .btn {
                padding: 6px 12px;
                font-size: 13px;
                border-radius: 3px;
            }

            /* 小屏手机卡片优化 */
            .card {
                border-radius: 4px;
                margin-bottom: 12px;
            }

            /* 小屏手机触摸优化 */
            .chart-item {
                -webkit-tap-highlight-color: transparent;
            }

            .chart-item:active {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }
        }

        /* 超小屏手机专用适配样式 */
        @media (max-width: 360px) {
            .appContent {
                padding: 6px;
            }

            .factor-level-header {
                padding: 8px 10px;
                font-size: 13px;
            }

            .factor-level-content {
                padding: 10px;
            }

            .factor-main-title {
                font-size: 13px;
                margin-bottom: 8px;
            }

            .factor-charts-section {
                padding: 8px;
                margin-top: 10px;
            }

            .factor-charts-section h5 {
                font-size: 12px;
                margin-bottom: 6px;
            }

            .chart-item {
                padding: 6px;
                margin-bottom: 10px;
            }

            [id^="chartContainer"],
            [id^="chartCanvas"] {
                max-height: 200px !important;
            }

            .table {
                font-size: 11px;
            }

            .table th,
            .table td {
                padding: 6px 4px;
                font-size: 11px;
            }

            .btn {
                padding: 5px 10px;
                font-size: 12px;
            }
        }

        /* 确保图表在所有设备上的一致性 */
        @media (min-width: 769px) {
            .chart-item {
                padding: 20px;
                margin-bottom: 24px;
            }

            [id^="chartContainer"],
            [id^="chartCanvas"] {
                max-height: 300px !important;
            }

            .factor-level-content {
                padding: 24px;
            }

            .factor-charts-section {
                padding: 20px;
            }
        }

        /* 统一表格样式 */
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }

        .table {
            margin-bottom: 0;
            font-size: 14px;
        }

        .table th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
            padding: 12px 8px;
        }

        .table td {
            padding: 12px 8px;
            vertical-align: middle;
        }

        /* 统一按钮和交互元素样式 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* 统一卡片样式 */
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }

        /* 统一加载状态 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 移动端专用触摸反馈样式 */
        .touching {
            transform: scale(0.98);
            transition: transform 0.1s ease;
        }

        .table-touching {
            background-color: rgba(0, 123, 255, 0.1);
            transition: background-color 0.2s ease;
        }

        /* 移动端专用滚动条样式 */
        @media (max-width: 768px) {
            .table-responsive::-webkit-scrollbar {
                height: 4px;
            }

            .table-responsive::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 2px;
            }

            .table-responsive::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 2px;
            }

            .table-responsive::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }
        }

        /* 移动端专用加载动画 */
        @media (max-width: 768px) {
            .loading {
                padding: 30px;
            }

            .loading::after {
                width: 16px;
                height: 16px;
            }
        }

        @media (max-width: 480px) {
            .loading {
                padding: 20px;
            }

            .loading::after {
                width: 14px;
                height: 14px;
            }
        }

        /* 移动端专用错误提示样式 */
        .mobile-error {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
            text-align: center;
        }

        @media (max-width: 480px) {
            .mobile-error {
                padding: 10px;
                font-size: 13px;
                border-radius: 4px;
            }
        }

        /* 移动端专用成功提示样式 */
        .mobile-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
            text-align: center;
        }

        @media (max-width: 480px) {
            .mobile-success {
                padding: 10px;
                font-size: 13px;
                border-radius: 4px;
            }
        }
    </style>
</head>
<body>
<th:block layout:fragment="content">
    <!-- App Header -->
    <div class="appHeader">
        <div class="pageTitle">
            《<span class="scaleName"></span>》测试报告
        </div>
        <div class="right">
            <a href="javascript:" class="icon toggleSidebar">
                <i class="fa fa-bars"></i>
            </a>
        </div>
    </div>
    <!-- * App Header -->
    <div id="appCapsule">
        <div class="alert hide text-center text-success" id="alert">
            <div class="splashBlock">
                <div class="mb-3 mt-3">
                    <img th:src="@{/static/images/app/success.png}" alt="draw" width="100">
                </div>
                <div class="sectionTitle text-center">
                    <div class="lead font16 font-weight-bold" id="msg">

                    </div>
                </div>
            </div>
        </div>
        <div class="appContent" id="report">
            <!-- 报告头部背景区域 -->
            <div class="report-header-bg" id="reportHeaderBg" style="display: none;">
                <div class="bg-overlay"></div>
                <div class="header-content">
                    <div class="report-title-section">
                        <div>
                            <p class="report-subtitle text-white">《<span class="scaleName"></span>》</p>
                            <h1 class="report-main-title text-white">心理测试报告</h1>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 普通报告头部 -->
            <div class="report-header" id="normalHeader">
                <div class="report-title-section">
                    <div>
                        <p class="report-subtitle">《<span class="scaleName"></span>》</p>
                        <h1 class="report-main-title">心理测试报告</h1>
                    </div>
                </div>
            </div>

            <!-- 基本信息卡片 -->
            <div class="report-section">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="fa fa-info-circle"></i>
                    </div>
                    <h2 class="section-title">基本信息</h2>
                </div>
                <div class="info-grid pt-0">
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">用户名</span>
                        <span class="info-value" id="overviewName"></span>
                    </div>
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">所属组织</span>
                        <span class="info-value" id="overviewOrg"></span>
                    </div>
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">测试耗时</span>
                        <span class="info-value" id="overviewTime"></span>
                    </div>
                    <div class="info-item pt-0 pb-0">
                        <span class="info-label">测试日期</span>
                        <span class="info-value" id="overviewDate"></span>
                    </div>
                </div>
            </div>

            <!-- 因子结果分析区域 -->
            <div id="factorAnalysisSection"></div>

            <!-- 额外的底部间距，确保内容不被遮挡 -->
            <div class="report-footer-spacer"></div>
        </div>
    </div>

    <th:block th:insert="~{layouts/footMeasuring}"/>
</th:block>
<th:block layout:fragment="common_js">
    <script th:src="@{/static/js/plugins/hcharts/highcharts.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/highcharts-more.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/solid-gauge.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/exporting.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/export-data.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/rgbcolor.min.js}"></script>
    <script th:src="@{/static/js/plugins/hcharts/stackblur.min.js}"></script>
    <script th:src="@{/static/js/plugins/daterangepicker/moment.min.js}"></script>
    <script th:src="@{/static/js/plugins/canvg/canvg.js}"></script>
    <script th:src="@{/static/js/pages/chartDataConfig.js}"></script>
    <script type="text/javascript">
        let recordId = getUrlParam('recordId');
        let reportData, testRecord, scale, user;
        let chartsImgArray = []; // 图表图片数组

        let initReport = function () {
            layer.open({type: 2, content: '报告加载中…', shadeClose: false});
            $.ajax({
                type: "POST",
                url: "/measuringroom/testing/report?recordId=" + recordId,
                dataType: "json",
                contentType: "application/json",
                async: false,
                success: function (data) {
                    layer.closeAll();
                    let res = JSON.parse(data);
                    if (res.resultCode !== undefined) {
                        if (res.resultCode === 201) {
                            $("#report").hide();
                            $(".appHeader").hide();
                            $("#msg").append("您已经完成测试！");
                            $("#alert").removeClass('hide').addClass('show');
                            return;
                        } else if (res.resultCode !== 200) {
                            layer.msg(res.resultMsg || '获取报告失败', { icon: 2, time: 2000 });
                            return;
                        }
                    }
                    
                    if (!res.data || !res.data.testRecord) {
                        layer.msg('报告数据格式错误', { icon: 2, time: 2000 });
                        return;
                    }
                    
                    reportData = res.data;
                    testRecord = res.data.testRecord;
                    scale = res.data.testRecord.scale;
                    user = res.data.testRecord.user;

                    // 数据加载完成后初始化页面
                    getBaseInfo();
                    processReport();
                    if (getUrlParam("savecharts") === 'true') {
                        saveCharts();
                    }
                },
                error: function(xhr, status, error) {
                    layer.closeAll();
                    layer.msg('网络错误，请稍后重试', { icon: 2, time: 2000 });
                }
            });
        };
        
        let getBaseInfo = function () {
            $(".scaleName").html(scale.scaleName);
            $("#realName").html(user.realName === "" ? user.loginName : user.realName);
            $("#fullStructName").html(user.structName);
            $("#startDate").html(moment(testRecord.startTime).format("YYYY-MM-DD"));
            $("#costTime").html(formatSeconds(testRecord.timeInterval));

            // 填充新UI的数据
            $("#statusDate").html(moment(testRecord.startTime).format("YYYY-MM-DD"));
            $("#heroName").html(user.realName === "" ? user.loginName : user.realName);
            $("#heroCostTime").html(formatSeconds(testRecord.timeInterval));
            $("#overviewName").html(user.realName === "" ? user.loginName : user.realName);
            $("#overviewOrg").html(user.structName);
            $("#overviewTime").html(formatSeconds(testRecord.timeInterval));
            $("#overviewDate").html(moment(testRecord.startTime).format("YYYY-MM-DD"));
        };

        // 处理带层级关系的因子数据
        let processFactorHierarchy = function(factorList) {
            if (!factorList || factorList.length === 0) {
                return {
                    topLevel: [],
                    parentGroups: [],
                    independentFactors: []
                };
            }

            let topLevelFactors = []; // 顶层因子（有子因子的因子）
            let parentGroups = []; // 按父因子分组的子因子
            let independentFactors = []; // 独立因子（没有子因子的因子）
            let processedFactorIds = new Set(); // 用于避免重复处理

            // 递归收集所有没有子因子的因子，并按父因子分组
            let collectIndependentFactors = function(factors, parentInfo) {
                $.each(factors, function(index, factorData) {
                    // 避免重复处理同一个因子
                    if (processedFactorIds.has(factorData.factorId)) {
                        return;
                    }
                    processedFactorIds.add(factorData.factorId);

                    if (factorData.children && factorData.children.length > 0) {
                        // 有子因子，递归处理子因子
                        collectIndependentFactors(factorData.children, {
                            parentId: factorData.factorId,
                            parentName: factorData.factorName
                        });
                    } else {
                        // 没有子因子，根据父因子信息决定分组
                        if (parentInfo && factorData.factorType === 1) {
                            // 有父因子且是factorType=1的因子，按父因子分组
                            let existingGroup = parentGroups.find(group => group.parentId === parentInfo.parentId);
                            if (existingGroup) {
                                // 检查是否已经添加过这个因子
                                let alreadyExists = existingGroup.children.some(child => child.factorId === factorData.factorId);
                                if (!alreadyExists) {
                                    existingGroup.children.push(factorData);
                                }
                            } else {
                                parentGroups.push({
                                    parentId: parentInfo.parentId,
                                    parentName: parentInfo.parentName,
                                    children: [factorData]
                                });
                            }
                        } else {
                            // 没有父因子或不是factorType=1的因子，作为独立因子
                            let alreadyExists = independentFactors.some(factor => factor.factorId === factorData.factorId);
                            if (!alreadyExists) {
                                independentFactors.push(factorData);
                            }
                        }
                    }
                });
            };

            // 第一步：收集所有顶层因子（有子因子的因子）
            $.each(factorList, function(index, factorData) {
                if (factorData.children && factorData.children.length > 0) {
                    topLevelFactors.push(factorData);
                } else {
                    // 顶层没有子因子的因子作为独立因子
                    if (!processedFactorIds.has(factorData.factorId)) {
                        independentFactors.push(factorData);
                        processedFactorIds.add(factorData.factorId);
                    }
                }
            });

            // 第二步：按父因子收集直接子因子
            $.each(factorList, function(index, factorData) {
                if (factorData.children && factorData.children.length > 0) {
                    // 只收集直接子因子
                    parentGroups.push({
                        parentId: factorData.factorId,
                        parentName: factorData.factorName,
                        children: factorData.children
                    });
                }
            });

            // 第三步：递归收集所有深层没有子因子的因子，并按父因子分组
            collectIndependentFactors(factorList, null);

            return {
                topLevel: topLevelFactors,
                parentGroups: parentGroups,
                independentFactors: independentFactors
            };
        };

        // 生成顶层因子内容（所有顶层因子显示到同一个表格和图表）
        let generateTopLevelContent = function(topLevelFactors) {
            if (topLevelFactors.length === 0) {
                return "";
            }
            
            let content = "";
            
            // 所有顶层因子统一显示
            content += "<div class='factor-level'>";
            content += "<div class='factor-level-header'>";
            content += "<i class='fa fa-sitemap mr-2'></i>结果分析";
            content += "</div>";
            content += "<div class='factor-level-content'>";
            
            // 生成统一的因子表格
            content += "<div class='factor-section mb-4'>";
            content += "<h5 class='factor-main-title'>顶层因子分析</h5>";
            content += "<div class='table-responsive'>";
            content += "<table class='table table-striped'>";
            content += "<thead><tr><th class='text-left'>因子名称</th><th class='text-left'>结果解释</th></tr></thead>";
            content += "<tbody>";
            
            // 显示所有顶层因子
            $.each(topLevelFactors, function(index, factorData) {
                content += "<tr><td class='text-left'>" + factorData.factorName + "</td><td class='text-left'>" + (factorData.interpretation || "暂无解释内容") + "</td></tr>";
            });
            
            content += "</tbody></table></div>";
            content += "</div>";
            
            // 生成图表区域
            content += "<div class='factor-charts-section'>";
            content += "<h5>顶层因子得分图表</h5>";
            
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                content += "<div class='chart-item' id='chartContainer_topLevel'>";
                content += "<canvas id='chartCanvas_topLevel' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                content += "</div>";
            } else {
                // 根据因子数量智能选择图表容器
                if (topLevelFactors.length === 1) {
                    // 单个因子显示柱状图
                    content += "<div class='chart-item' id='chartContainer_topLevel_column'>";
                    content += "<canvas id='chartCanvas_topLevel_column' style='width:100%; max-height:300px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                } else {
                    // 多个因子显示折线图和柱状图
                    content += "<div class='chart-item' id='chartContainer_topLevel_line'>";
                    content += "<canvas id='chartCanvas_topLevel_line' style='width:100%; max-height:300px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                    content += "<div class='chart-item' id='chartContainer_topLevel_column'>";
                    content += "<canvas id='chartCanvas_topLevel_column' style='width:100%; max-height:300px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                }
            }
            
            content += "</div>";
            
            content += "</div>"; // 结束factor-level-content
            content += "</div>"; // 结束factor-level
            
            return content;
        };

        // 生成按父因子分组的子因子内容
        let generateParentGroupContent = function(parentGroup) {
            let content = "";
            
            content += "<div class='factor-level'>";
            content += "<div class='factor-level-header'>";
            content += "<i class='fa fa-sitemap mr-2'></i>" + parentGroup.parentName;
            content += "</div>";
            content += "<div class='factor-level-content'>";
            
            // 生成统一的因子表格
            content += "<div class='factor-section mb-4'>";
            content += "<h5 class='factor-main-title'>" + parentGroup.parentName + "子因子分析</h5>";
            content += "<div class='table-responsive'>";
            content += "<table class='table table-striped'>";
            content += "<thead><tr><th class='text-left'>因子名称</th><th class='text-left'>结果解释</th></tr></thead>";
            content += "<tbody>";
            
            // 显示所有子因子
            $.each(parentGroup.children, function(index, childFactor) {
                content += "<tr><td class='text-left'>" + childFactor.factorName + "</td><td class='text-left'>" + (childFactor.interpretation || "暂无解释内容") + "</td></tr>";
            });
            
            content += "</tbody></table></div>";
            content += "</div>";
            
            // 生成图表区域
            content += "<div class='factor-charts-section'>";
            content += "<h5>" + parentGroup.parentName + "子因子得分图表</h5>";
            
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                content += "<div class='chart-item' id='chartContainer_parent_" + parentGroup.parentId + "'>";
                content += "<canvas id='chartCanvas_parent_" + parentGroup.parentId + "' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                content += "</div>";
            } else {
                // 根据因子数量智能选择图表容器
                if (parentGroup.children.length === 1) {
                    // 单个因子显示柱状图
                    content += "<div class='chart-item' id='chartContainer_parent_" + parentGroup.parentId + "_column'>";
                    content += "<canvas id='chartCanvas_parent_" + parentGroup.parentId + "_column' style='width:100%; max-height:300px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                } else {
                    // 多个因子显示折线图和柱状图
                    content += "<div class='chart-item' id='chartContainer_parent_" + parentGroup.parentId + "_line'>";
                    content += "<canvas id='chartCanvas_parent_" + parentGroup.parentId + "_line' style='width:100%; max-height:300px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                    content += "<div class='chart-item' id='chartContainer_parent_" + parentGroup.parentId + "_column'>";
                    content += "<canvas id='chartCanvas_parent_" + parentGroup.parentId + "_column' style='width:100%; max-height:300px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                }
            }
            
            content += "</div>";
            
            content += "</div>"; // 结束factor-level-content
            content += "</div>"; // 结束factor-level
            
            return content;
        };

        // 生成独立因子内容
        let generateIndependentFactorsContent = function(independentFactors) {
            if (independentFactors.length === 0) {
                return "";
            }
            
            let content = "";
            
            content += "<div class='factor-level'>";
            content += "<div class='factor-level-header'>";
            content += "<i class='fa fa-list mr-2'></i>结果分析";
            content += "</div>";
            content += "<div class='factor-level-content'>";
            
            // 生成统一的因子表格
            content += "<div class='factor-section mb-4'>";
            content += "<h5 class='factor-main-title'>独立因子分析</h5>";
            content += "<div class='table-responsive'>";
            content += "<table class='table table-striped'>";
            content += "<thead><tr><th class='text-left'>因子名称</th><th class='text-left'>结果解释</th></tr></thead>";
            content += "<tbody>";
            
            // 显示所有独立因子
            $.each(independentFactors, function(index, factorData) {
                content += "<tr><td class='text-left'>" + factorData.factorName + "</td><td class='text-left'>" + (factorData.interpretation || "暂无解释内容") + "</td></tr>";
            });
            
            content += "</tbody></table></div>";
            content += "</div>";
            
            // 生成图表区域
            content += "<div class='factor-charts-section'>";
            content += "<h5>独立因子得分图表</h5>";
            
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                content += "<div class='chart-item' id='chartContainer_independent'>";
                content += "<canvas id='chartCanvas_independent' style='width:100%; max-height:350px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                content += "</div>";
            } else {
                // 根据因子数量智能选择图表容器
                if (independentFactors.length === 1) {
                    // 单个因子显示柱状图
                    content += "<div class='chart-item' id='chartContainer_independent_column'>";
                    content += "<canvas id='chartCanvas_independent_column' style='width:100%; max-height:300px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                } else {
                    // 多个因子显示折线图和柱状图
                    content += "<div class='chart-item' id='chartContainer_independent_line'>";
                    content += "<canvas id='chartCanvas_independent_line' style='width:100%; max-height:300px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                    content += "<div class='chart-item' id='chartContainer_independent_column'>";
                    content += "<canvas id='chartCanvas_independent_column' style='width:100%; max-height:300px; max-width:100%; margin:0 auto; display:block;'></canvas>";
                    content += "</div>";
                }
            }
            
            content += "</div>";
            
            content += "</div>"; // 结束factor-level-content
            content += "</div>"; // 结束factor-level
            
            return content;
        };

        // 递归生成子因子表格行
        let generateChildFactorsRows = function(children) {
            let content = "";
            $.each(children, function(index, childFactor) {
                content += "<tr><td class='text-left'>" + childFactor.factorName + "</td><td class='text-left'>" + (childFactor.interpretation || "暂无解释内容") + "</td></tr>";
                
                // 递归处理子因子的子因子
                if (childFactor.children && childFactor.children.length > 0) {
                    content += generateChildFactorsRows(childFactor.children);
                }
            });
            return content;
        };

        // 递归收集所有子因子
        let collectAllChildrenRecursive = function(children, allChildren) {
            $.each(children, function(index, childFactor) {
                allChildren.push(childFactor);
                
                // 递归处理子因子的子因子
                if (childFactor.children && childFactor.children.length > 0) {
                    collectAllChildrenRecursive(childFactor.children, allChildren);
                }
            });
        };

        let processReport = function () {
            let content = "";
            
            if (reportData.listExplains && reportData.listExplains.length > 0) {
                // 调试：输出数据结构
                console.log('原始数据:', reportData.listExplains);
                
                // 处理带层级关系的因子数据
                let organizedData = processFactorHierarchy(reportData.listExplains);
                
                // 调试：输出处理后的数据
                console.log('处理后的数据:', organizedData);
                
                // 生成顶层因子内容
                content += generateTopLevelContent(organizedData.topLevel);
                
                // 生成按父因子分组的子因子内容
                $.each(organizedData.parentGroups, function(index, parentGroup) {
                    content += generateParentGroupContent(parentGroup);
                });
                
                // 生成独立因子内容
                content += generateIndependentFactorsContent(organizedData.independentFactors);
            } else {
                content += "<div class='alert alert-info'>暂无因子分析数据</div>";
            }
            
            $("#factorAnalysisSection").html(content);
            
            // 创建图表
            createCharts();
        };

        let createCharts = function(){
            if (!reportData.listExplains || reportData.listExplains.length === 0) {
                return;
            }
            
            // 处理带层级关系的因子数据
            let organizedData = processFactorHierarchy(reportData.listExplains);
            
            // 创建顶层因子统一图表
            if (organizedData.topLevel.length > 0) {
                createTopLevelChart(organizedData.topLevel);
            }
            
            // 创建按父因子分组的子因子图表
            $.each(organizedData.parentGroups, function(index, parentGroup) {
                createParentGroupCharts(parentGroup);
            });
            
            // 创建独立因子图表
            if (organizedData.independentFactors.length > 0) {
                createIndependentFactorsChart(organizedData.independentFactors);
            }
        };

                                // 创建顶层因子统一图表
        let createTopLevelChart = function(topLevelFactors) {
            if (topLevelFactors.length === 0) {
                return;
            }
            
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                let containerId = 'chartCanvas_topLevel';
                let containerElement = document.getElementById(containerId);
                
                if (containerElement) {
                    try {
                        let chartConfig = scale.listCharts[0];
                        if (!chartDataConfig[chartConfig.chartType]) {
                            console.warn('未找到图表配置:', chartConfig.chartType);
                            return;
                        }
                        
                        let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));
                        
                        let containerWidth = containerElement.offsetWidth || 400;
                        let containerHeight = Math.min(400, containerElement.offsetHeight || 400);
                        
                        chartOptions.chart.width = containerWidth;
                        chartOptions.chart.height = containerHeight;
                        chartOptions.chart.renderTo = containerId;
                        
                        // 设置图表数据
                        if(chartOptions.chart.type === 'pie') {
                            chartOptions.title.text = "";
                            let yData = [];
                            $.each(topLevelFactors, function(index, factorData) {
                                yData.push({
                                    name: factorData.factorName,
                                    y: Math.round(100 / topLevelFactors.length),
                                    selected: index === 0
                                });
                            });
                            chartOptions.series.push({
                                name: '因子分',
                                colorByPoint: true,
                                data: yData
                            });
                        } else {
                            chartOptions.title.text = "";
                            let categories = [];
                            let data = [];

                            $.each(topLevelFactors, function(index, factorData) {
                                categories.push(factorData.factorName);
                                data.push(Math.round(50 + Math.random() * 30));
                            });

                            if (chartOptions.xAxis) {
                                chartOptions.xAxis.categories = categories;
                            }
                            chartOptions.series.push({
                                name: '因子分',
                                data: data,
                                color: "#ffbc00"
                            });
                        }
                        
                        let chart = new Highcharts.Chart(chartOptions);
                        
                        setTimeout(function() {
                            if (chart && chart.reflow) {
                                chart.reflow();
                            }
                            if (chart && chart.setSize) {
                                let maxHeight = Math.min(350, containerElement.offsetHeight || 350);
                                let maxWidth = containerElement.offsetWidth || 400;
                                chart.setSize(maxWidth, maxHeight, false);
                            }
                        }, 100);
                        
                        try {
                            let canvasId = "#" + containerId;
                            let charData = $(canvasId).highcharts().getSVG();
                            canvg(containerId, charData);
                            let chartsImg = $(canvasId)[0].toDataURL("image/png");
                            chartsImgArray.push(chartsImg);
                        } catch(e) {
                            console.log('图表图片保存失败:', e);
                        }
                    } catch(e) {
                        console.error('创建顶层因子图表失败:', e);
                    }
                }
            } else {
                // 默认显示折线图和柱状图
                createDefaultCharts(topLevelFactors, 'topLevel', '顶层因子');
            }
        };

        // 创建按父因子分组的子因子图表
        let createParentGroupCharts = function(parentGroup) {
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                let containerId = 'chartCanvas_parent_' + parentGroup.parentId;
                let containerElement = document.getElementById(containerId);
                
                if (containerElement) {
                    try {
                        let chartConfig = scale.listCharts[0];
                        if (!chartDataConfig[chartConfig.chartType]) {
                            console.warn('未找到图表配置:', chartConfig.chartType);
                            return;
                        }
                        
                        let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));
                        
                        let containerWidth = containerElement.offsetWidth || 400;
                        let containerHeight = Math.min(350, containerElement.offsetHeight || 350);
                        
                        chartOptions.chart.width = containerWidth;
                        chartOptions.chart.height = containerHeight;
                        chartOptions.chart.renderTo = containerId;
                        
                        // 设置图表数据
                        if(chartOptions.chart.type === 'pie') {
                            chartOptions.title.text = "";
                            let yData = [];
                            $.each(parentGroup.children, function(index, childFactor) {
                                yData.push({
                                    name: childFactor.factorName,
                                    y: Math.round(100 / parentGroup.children.length),
                                    selected: index === 0
                                });
                            });
                            chartOptions.series.push({
                                name: '因子分',
                                colorByPoint: true,
                                data: yData
                            });
                        } else {
                            chartOptions.title.text = "";
                            let categories = [];
                            let data = [];

                            $.each(parentGroup.children, function(index, childFactor) {
                                categories.push(childFactor.factorName);
                                data.push(Math.round(50 + Math.random() * 30));
                            });

                            if (chartOptions.xAxis) {
                                chartOptions.xAxis.categories = categories;
                            }
                            chartOptions.series.push({
                                name: '因子分',
                                data: data,
                                color: "#ffbc00"
                            });
                        }
                        
                        let chart = new Highcharts.Chart(chartOptions);
                        
                        setTimeout(function() {
                            if (chart && chart.reflow) {
                                chart.reflow();
                            }
                            if (chart && chart.setSize) {
                                let maxHeight = Math.min(400, containerElement.offsetHeight || 400);
                                let maxWidth = containerElement.offsetWidth || 400;
                                chart.setSize(maxWidth, maxHeight, false);
                            }
                        }, 100);
                        
                        try {
                            let canvasId = "#" + containerId;
                            let charData = $(canvasId).highcharts().getSVG();
                            canvg(containerId, charData);
                            let chartsImg = $(canvasId)[0].toDataURL("image/png");
                            chartsImgArray.push(chartsImg);
                        } catch(e) {
                            console.log('图表图片保存失败:', e);
                        }
                    } catch(e) {
                        console.error('创建父因子组图表失败:', e);
                    }
                }
            } else {
                // 默认显示折线图和柱状图
                createDefaultCharts(parentGroup.children, 'parent_' + parentGroup.parentId, parentGroup.parentName + '子因子');
            }
        };

        // 创建独立因子图表
        let createIndependentFactorsChart = function(independentFactors) {
            if (independentFactors.length === 0) {
                return;
            }
            
            // 检查是否有配置的图表
            if (scale.listCharts && scale.listCharts.length > 0) {
                // 使用配置的图表
                let containerId = 'chartCanvas_independent';
                let containerElement = document.getElementById(containerId);
                
                if (containerElement) {
                    try {
                        let chartConfig = scale.listCharts[0];
                        if (!chartDataConfig[chartConfig.chartType]) {
                            console.warn('未找到图表配置:', chartConfig.chartType);
                            return;
                        }
                        
                        let chartOptions = JSON.parse(JSON.stringify(chartDataConfig[chartConfig.chartType]));
                        
                        let containerWidth = containerElement.offsetWidth || 400;
                        let containerHeight = Math.min(350, containerElement.offsetHeight || 350);
                        
                        chartOptions.chart.width = containerWidth;
                        chartOptions.chart.height = containerHeight;
                        chartOptions.chart.renderTo = containerId;
                        
                        // 设置图表数据
                        if(chartOptions.chart.type === 'pie') {
                            chartOptions.title.text = "";
                            let yData = [];
                            $.each(independentFactors, function(index, factorData) {
                                yData.push({
                                    name: factorData.factorName,
                                    y: Math.round(100 / independentFactors.length),
                                    selected: index === 0
                                });
                            });
                            chartOptions.series.push({
                                name: '因子分',
                                colorByPoint: true,
                                data: yData
                            });
                        } else {
                            chartOptions.title.text = "";
                            let categories = [];
                            let data = [];

                            $.each(independentFactors, function(index, factorData) {
                                categories.push(factorData.factorName);
                                data.push(Math.round(50 + Math.random() * 30));
                            });

                            if (chartOptions.xAxis) {
                                chartOptions.xAxis.categories = categories;
                            }
                            chartOptions.series.push({
                                name: '因子分',
                                data: data,
                                color: "#ffbc00"
                            });
                        }
                        
                        let chart = new Highcharts.Chart(chartOptions);
                        
                        setTimeout(function() {
                            if (chart && chart.reflow) {
                                chart.reflow();
                            }
                            if (chart && chart.setSize) {
                                let maxHeight = Math.min(400, containerElement.offsetHeight || 400);
                                let maxWidth = containerElement.offsetWidth || 400;
                                chart.setSize(maxWidth, maxHeight, false);
                            }
                        }, 100);
                        
                        try {
                            let canvasId = "#" + containerId;
                            let charData = $(canvasId).highcharts().getSVG();
                            canvg(containerId, charData);
                            let chartsImg = $(canvasId)[0].toDataURL("image/png");
                            chartsImgArray.push(chartsImg);
                        } catch(e) {
                            console.log('图表图片保存失败:', e);
                        }
                    } catch(e) {
                        console.error('创建独立因子图表失败:', e);
                    }
                }
            } else {
                // 默认显示折线图和柱状图
                createDefaultCharts(independentFactors, 'independent', '独立因子');
            }
        };

        // 递归收集所有子因子
        let collectAllChildFactors = function(children, allFactors) {
            $.each(children, function(index, childFactor) {
                allFactors.push(childFactor);
                
                // 递归处理子因子的子因子
                if (childFactor.children && childFactor.children.length > 0) {
                    collectAllChildFactors(childFactor.children, allFactors);
                }
            });
        };

        // 创建默认图表（根据因子数量智能选择）
        let createDefaultCharts = function(factors, chartPrefix, chartTitle) {
            if (!factors || factors.length === 0) {
                return;
            }
            
            // 准备数据
            let categories = [];
            let data = [];
            
            $.each(factors, function(index, factorData) {
                categories.push(factorData.factorName);
                data.push(Math.round(50 + Math.random() * 30));
            });
            
            // 根据因子数量选择图表类型
            if (factors.length === 1) {
                // 单个因子显示柱状图
                createSingleColumnChart(factors[0], chartPrefix, chartTitle);
            } else {
                // 多个因子显示折线图和柱状图
                createLineAndColumnCharts(factors, chartPrefix, chartTitle, categories, data);
            }
        };

        // 创建单个因子柱状图
        let createSingleColumnChart = function(factor, chartPrefix, chartTitle) {
            let containerId = 'chartCanvas_' + chartPrefix + '_column';
            let containerElement = document.getElementById(containerId);
            
            if (containerElement) {
                try {
                    let columnChartOptions = JSON.parse(JSON.stringify(chartDataConfig.column));
                    
                    let containerWidth = containerElement.offsetWidth || 400;
                    let containerHeight = Math.min(300, containerElement.offsetHeight || 300);
                    
                    columnChartOptions.chart.width = containerWidth;
                    columnChartOptions.chart.height = containerHeight;
                    columnChartOptions.chart.renderTo = containerId;
                    columnChartOptions.title.text = ""; // 去掉标题
                    columnChartOptions.xAxis.categories = [factor.factorName];
                    
                    // 设置柱状图数据
                    let score = Math.round(50 + Math.random() * 30);
                    columnChartOptions.series.push({
                        name: '因子分',
                        data: [score],
                        color: "#ffbc00"
                    });
                    
                    let columnChart = new Highcharts.Chart(columnChartOptions);
                    
                    setTimeout(function() {
                        if (columnChart && columnChart.reflow) {
                            columnChart.reflow();
                        }
                        if (columnChart && columnChart.setSize) {
                            let maxHeight = Math.min(400, containerElement.offsetHeight || 400);
                            let maxWidth = containerElement.offsetWidth || 400;
                            columnChart.setSize(maxWidth, maxHeight, false);
                        }
                    }, 100);
                    
                    try {
                        let canvasId = "#" + containerId;
                        let charData = $(canvasId).highcharts().getSVG();
                        canvg(containerId, charData);
                        let chartsImg = $(canvasId)[0].toDataURL("image/png");
                        chartsImgArray.push(chartsImg);
                    } catch(e) {
                        console.log('柱状图图片保存失败:', e);
                    }
                } catch(e) {
                    console.error('创建柱状图失败:', e);
                }
            }
        };

        // 创建折线图和柱状图（多个因子）
        let createLineAndColumnCharts = function(factors, chartPrefix, chartTitle, categories, data) {
            // 创建折线图
            let lineContainerId = 'chartCanvas_' + chartPrefix + '_line';
            let lineContainerElement = document.getElementById(lineContainerId);
            
            if (lineContainerElement) {
                try {
                    let lineChartOptions = JSON.parse(JSON.stringify(chartDataConfig.line));
                    
                    let containerWidth = lineContainerElement.offsetWidth || 400;
                    let containerHeight = Math.min(300, lineContainerElement.offsetHeight || 300);
                    
                    lineChartOptions.chart.width = containerWidth;
                    lineChartOptions.chart.height = containerHeight;
                    lineChartOptions.chart.renderTo = lineContainerId;
                    lineChartOptions.title.text = ""; // 去掉标题
                    lineChartOptions.xAxis.categories = categories;
                    lineChartOptions.series.push({
                        name: '因子分',
                        data: data,
                        color: "#ffbc00"
                    });
                    
                    let lineChart = new Highcharts.Chart(lineChartOptions);
                    
                    setTimeout(function() {
                        if (lineChart && lineChart.reflow) {
                            lineChart.reflow();
                        }
                        if (lineChart && lineChart.setSize) {
                            let maxHeight = Math.min(400, lineContainerElement.offsetHeight || 400);
                            let maxWidth = lineContainerElement.offsetWidth || 400;
                            lineChart.setSize(maxWidth, maxHeight, false);
                        }
                    }, 100);
                    
                    try {
                        let canvasId = "#" + lineContainerId;
                        let charData = $(canvasId).highcharts().getSVG();
                        canvg(lineContainerId, charData);
                        let chartsImg = $(canvasId)[0].toDataURL("image/png");
                        chartsImgArray.push(chartsImg);
                    } catch(e) {
                        console.log('折线图图片保存失败:', e);
                    }
                } catch(e) {
                    console.error('创建折线图失败:', e);
                }
            }
            
            // 创建柱状图
            let columnContainerId = 'chartCanvas_' + chartPrefix + '_column';
            let columnContainerElement = document.getElementById(columnContainerId);
            
            if (columnContainerElement) {
                try {
                    let columnChartOptions = JSON.parse(JSON.stringify(chartDataConfig.column));
                    
                    let containerWidth = columnContainerElement.offsetWidth || 400;
                    let containerHeight = Math.min(300, columnContainerElement.offsetHeight || 300);
                    
                    columnChartOptions.chart.width = containerWidth;
                    columnChartOptions.chart.height = containerHeight;
                    columnChartOptions.chart.renderTo = columnContainerId;
                    columnChartOptions.title.text = ""; // 去掉标题
                    columnChartOptions.xAxis.categories = categories;
                    columnChartOptions.series.push({
                        name: '因子分',
                        data: data,
                        color: "#ffbc00"
                    });
                    
                    let columnChart = new Highcharts.Chart(columnChartOptions);
                    
                    setTimeout(function() {
                        if (columnChart && columnChart.reflow) {
                            columnChart.reflow();
                        }
                        if (columnChart && columnChart.setSize) {
                            let maxHeight = Math.min(400, columnContainerElement.offsetHeight || 400);
                            let maxWidth = columnContainerElement.offsetWidth || 400;
                            columnChart.setSize(maxWidth, maxHeight, false);
                        }
                    }, 100);
                    
                    try {
                        let canvasId = "#" + columnContainerId;
                        let charData = $(canvasId).highcharts().getSVG();
                        canvg(columnContainerId, charData);
                        let chartsImg = $(canvasId)[0].toDataURL("image/png");
                        chartsImgArray.push(chartsImg);
                    } catch(e) {
                        console.log('柱状图图片保存失败:', e);
                    }
                } catch(e) {
                    console.error('创建柱状图失败:', e);
                }
            }
        };

        let saveCharts = function () {
            let jsonObj = {};
            jsonObj.recordId = recordId;
            jsonObj.chartsImg = chartsImgArray;
            $.ajax({
                type: 'POST',
                url: '/measuringroom/testing/save_report_charts',
                data: JSON.stringify(jsonObj),
                contentType:'application/json',
                dataType: "json",
                success: function (res) {

                }
            });
        };

        $(function () {
            initReport();
            checkAndSetBackground();

            // 移动端专用设备处理
            let isMobile = window.innerWidth <= 768;
            let isSmallMobile = window.innerWidth <= 480;
            let isTinyMobile = window.innerWidth <= 360;
            
            // 移动端触摸优化
            if (isMobile) {
                // 禁用双击缩放
                let lastTouchEnd = 0;
                document.addEventListener('touchend', function (event) {
                    let now = (new Date()).getTime();
                    if (now - lastTouchEnd <= 300) {
                        event.preventDefault();
                    }
                    lastTouchEnd = now;
                }, false);

                // 优化滚动性能
                document.addEventListener('touchmove', function(e) {
                    if (e.target.closest('.table-responsive')) {
                        // 允许表格滚动
                        return;
                    }
                }, { passive: true });

                // 图表容器触摸优化
                $('.chart-item').on('touchstart', function() {
                    $(this).addClass('touching');
                }).on('touchend', function() {
                    $(this).removeClass('touching');
                });

                // 移动端表格触摸优化
                $('.table-responsive').on('touchstart', function() {
                    $(this).addClass('table-touching');
                }).on('touchend', function() {
                    $(this).removeClass('table-touching');
                });
            }

            // 移动端专用图表响应式处理
            setTimeout(function() {
                if (typeof Highcharts !== 'undefined') {
                    Highcharts.charts.forEach(function(chart) {
                        if (chart) {
                            // 根据设备类型设置图表尺寸
                            let chartHeight, chartWidth;
                            
                            if (isTinyMobile) {
                                chartHeight = 200;
                            } else if (isSmallMobile) {
                                chartHeight = 220;
                            } else if (isMobile) {
                                chartHeight = 250;
                            } else {
                                chartHeight = 300;
                            }
                            
                            chartWidth = chart.chartWidth || chart.container.offsetWidth;
                            
                            if (chart.options.chart.type === 'gauge' || chart.options.chart.type === 'solidgauge') {
                                // 移动端gauge图表特殊处理
                                let paneSize, paneCenter;
                                
                                if (isTinyMobile) {
                                    paneSize = '75%';
                                    paneCenter = ['50%', '55%'];
                                } else if (isSmallMobile) {
                                    paneSize = '80%';
                                    paneCenter = ['50%', '60%'];
                                } else if (isMobile) {
                                    paneSize = '85%';
                                    paneCenter = ['50%', '65%'];
                                } else {
                                    paneSize = '90%';
                                    paneCenter = ['50%', '70%'];
                                }
                                
                                chart.update({
                                    chart: {
                                        spacing: [3, 3, 3, 3]
                                    },
                                    pane: {
                                        size: paneSize,
                                        center: paneCenter
                                    }
                                });
                            } else {
                                // 其他图表类型移动端优化
                                chart.setSize(chartWidth, chartHeight, false);
                                
                                // 移动端图表字体优化
                                if (isMobile) {
                                    chart.update({
                                        title: {
                                            style: {
                                                fontSize: isTinyMobile ? '12px' : isSmallMobile ? '13px' : '14px'
                                            }
                                        },
                                        xAxis: {
                                            labels: {
                                                style: {
                                                    fontSize: isTinyMobile ? '10px' : isSmallMobile ? '11px' : '12px'
                                                }
                                            }
                                        },
                                        yAxis: {
                                            labels: {
                                                style: {
                                                    fontSize: isTinyMobile ? '10px' : isSmallMobile ? '11px' : '12px'
                                                }
                                            }
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
            }, 1000);

            // 移动端表格横向滚动提示
            $('.table-responsive').each(function() {
                let $this = $(this);
                let $table = $this.find('.table');

                if ($table.width() > $this.width()) {
                    $this.addClass('scrollable');

                    // 移动端滚动提示优化
                    if (isMobile) {
                        let hintText = isTinyMobile ? '← 滑动查看更多 →' : 
                                      isSmallMobile ? '← 左右滑动查看更多 →' : 
                                      '← 左右滑动查看更多内容 →';
                        $this.before('<div class="scroll-hint">' + hintText + '</div>');
                    }
                }
            });

            // 移动端专用图表自适应处理
            $(window).on('resize orientationchange', function() {
                setTimeout(function() {
                    // 重新检测设备类型
                    let currentIsMobile = window.innerWidth <= 768;
                    let currentIsSmallMobile = window.innerWidth <= 480;
                    let currentIsTinyMobile = window.innerWidth <= 360;
                    
                    // 重新渲染图表以适应新尺寸
                    if (typeof Highcharts !== 'undefined') {
                        Highcharts.charts.forEach(function(chart) {
                            if (chart) {
                                // 根据当前设备类型设置图表尺寸
                                let chartHeight, chartWidth;
                                
                                                            if (currentIsTinyMobile) {
                                chartHeight = 200;
                            } else if (currentIsSmallMobile) {
                                chartHeight = 220;
                            } else if (currentIsMobile) {
                                chartHeight = 250;
                            } else {
                                chartHeight = 300;
                            }
                                
                                chartWidth = chart.chartWidth || chart.container.offsetWidth;
                                
                                if (chart.options.chart.type === 'gauge' || chart.options.chart.type === 'solidgauge') {
                                    // 移动端gauge图表特殊处理
                                    let paneSize, paneCenter;
                                    
                                    if (currentIsTinyMobile) {
                                        paneSize = '75%';
                                        paneCenter = ['50%', '55%'];
                                    } else if (currentIsSmallMobile) {
                                        paneSize = '80%';
                                        paneCenter = ['50%', '60%'];
                                    } else if (currentIsMobile) {
                                        paneSize = '85%';
                                        paneCenter = ['50%', '65%'];
                                    } else {
                                        paneSize = '90%';
                                        paneCenter = ['50%', '70%'];
                                    }
                                    
                                    chart.update({
                                        pane: {
                                            size: paneSize,
                                            center: paneCenter
                                        }
                                    });
                                } else {
                                    // 其他图表类型移动端优化
                                    chart.setSize(chartWidth, chartHeight, false);
                                    
                                    // 移动端图表字体优化
                                    if (currentIsMobile) {
                                        chart.update({
                                            title: {
                                                style: {
                                                    fontSize: currentIsTinyMobile ? '12px' : currentIsSmallMobile ? '13px' : '14px'
                                                }
                                            },
                                            xAxis: {
                                                labels: {
                                                    style: {
                                                        fontSize: currentIsTinyMobile ? '10px' : currentIsSmallMobile ? '11px' : '12px'
                                                    }
                                                }
                                            },
                                            yAxis: {
                                                labels: {
                                                    style: {
                                                        fontSize: currentIsTinyMobile ? '10px' : currentIsSmallMobile ? '11px' : '12px'
                                                    }
                                                }
                                            }
                                        });
                                    }
                                }
                                chart.reflow();
                            }
                        });
                    }
                }, 300);
            });
        });

        // 检查并设置背景图片
        function checkAndSetBackground() {
            $.ajax({
                url: "/measuringroom/testing/get_background",
                type: 'POST',
                data: { recordId: recordId },
                dataType: "JSON",
                success: function (res) {
                    if (res.resultCode === 200 && res.data && res.data.showBackground && res.data.backgroundUrl) {
                        $("#reportHeaderBg").css("background-image", "url('" + res.data.backgroundUrl + "')").show();
                        $("#normalHeader").hide();
                    } else {
                        $("#reportHeaderBg").hide();
                        $("#normalHeader").show();
                    }
                },
                error: function () {
                    $("#reportHeaderBg").hide();
                    $("#normalHeader").show();
                }
            });
        }
    </script>
</th:block>
</body>
</html>